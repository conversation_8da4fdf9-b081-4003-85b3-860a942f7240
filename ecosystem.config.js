export default {
  apps: [
    {
      name: 'khobra-kitchens',
      script: 'npm',
      args: 'run preview',
      cwd: '/var/www/html',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 4173
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 4173
      },
      error_file: '/var/log/pm2/khobra-kitchens-error.log',
      out_file: '/var/log/pm2/khobra-kitchens-out.log',
      log_file: '/var/log/pm2/khobra-kitchens-combined.log',
      time: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      kill_timeout: 5000,
      restart_delay: 1000,
      max_restarts: 10,
      min_uptime: '10s'
    },
    {
      name: 'khobra-nginx-monitor',
      script: 'bash',
      args: '-c "while true; do nginx -t && sleep 300 || (echo "Nginx config error detected" && sudo systemctl reload nginx); done"',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '100M',
      env: {
        NODE_ENV: 'production'
      },
      error_file: '/var/log/pm2/nginx-monitor-error.log',
      out_file: '/var/log/pm2/nginx-monitor-out.log',
      log_file: '/var/log/pm2/nginx-monitor-combined.log',
      time: true
    }
  ],

  deploy: {
    production: {
      user: 'root',
      host: '************',
      ref: 'origin/main',
      repo: '**************:username/khobra-kitchens.git',
      path: '/var/www/html',
      'pre-deploy-local': '',
      'post-deploy': 'npm install --legacy-peer-deps && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
}
