const Footer = () => {
  const quickLinks = [
    { href: "#home", text: "الرئيسية" },
    { href: "#why-us", text: "لماذا نحن" },
    { href: "#kitchens", text: "المطابخ" },
    { href: "#cabinets", text: "الخزائن" },
    { href: "#contact", text: "تواصل معنا" }
  ];

  const services = [
    "تصميم وتنفيذ المطابخ",
    "تصميم وتنفيذ خزائن الملابس",
    "تصميم وحدات التلفزيون",
    "تصميم المكتبات والرفوف",
    "تصميم خزائن الحمامات"
  ];

  const contactInfo = [
    {
      icon: "ri-map-pin-line",
      text: "شارع الملك فهد، الرياض، المملكة العربية السعودية"
    },
    {
      icon: "ri-phone-line",
      text: "+966 12 345 6789"
    },
    {
      icon: "ri-mail-line",
      text: "<EMAIL>"
    },
    {
      icon: "ri-time-line",
      text: "السبت - الخميس: 9 صباحاً - 9 مساءً"
    }
  ];

  const socialLinks = [
    { href: "#", icon: "ri-instagram-line", title: "Instagram" },
    { href: "#", icon: "ri-tiktok-line", title: "TikTok" },
    { href: "#", icon: "ri-snapchat-line", title: "Snapchat" },
    { href: "#", icon: "ri-whatsapp-line", title: "WhatsApp" }
  ];

  return (
    <footer id="contact" className="bg-gray-900 text-white pt-16 pb-8">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-10">
          {/* Company Info */}
          <div>
            <div className="font-['Pacifico'] text-white text-3xl mb-6">logo</div>
            <p className="text-gray-400 mb-6">
              نحول مساحات منزلك إلى تحف فنية تجمع بين الجمال والوظيفة بلمسة من الإبداع
            </p>
            <div className="flex space-x-4 rtl:space-x-reverse">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  className="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center hover:bg-primary transition-colors duration-300"
                  title={social.title}
                >
                  <i className={social.icon}></i>
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-bold mb-6">روابط سريعة</h3>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors duration-300"
                  >
                    {link.text}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-xl font-bold mb-6">خدماتنا</h3>
            <ul className="space-y-3">
              {services.map((service, index) => (
                <li key={index} className="text-gray-400">
                  {service}
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-xl font-bold mb-6">تواصل معنا</h3>
            <ul className="space-y-3">
              {contactInfo.map((info, index) => (
                <li key={index} className="flex items-start space-x-3 rtl:space-x-reverse">
                  <div className="w-5 h-5 flex items-center justify-center mt-1">
                    <i className={`${info.icon} text-primary`}></i>
                  </div>
                  <span className="text-gray-400">{info.text}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500">
          <p>© 2025 عجائب الخبراء. جميع الحقوق محفوظة.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
