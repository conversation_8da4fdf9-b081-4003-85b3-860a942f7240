// Environment Configuration
// This file manages all environment variables and provides secure access

// Helper function to decode base64
const decodeBase64 = (encoded) => {
  try {
    return atob(encoded)
  } catch (error) {
    console.error('Failed to decode base64:', error)
    return ''
  }
}

// Helper function to encode base64 (for development)
const encodeBase64 = (text) => {
  try {
    return btoa(text)
  } catch (error) {
    console.error('Failed to encode base64:', error)
    return ''
  }
}

// Application Configuration
export const APP_CONFIG = {
  name: import.meta.env.VITE_APP_NAME || 'خبرة المطابخ',
  domain: import.meta.env.VITE_APP_DOMAIN || 'khobrakitchens.com',
  url: import.meta.env.VITE_APP_URL || 'https://khobrakitchens.com',
  serverIP: import.meta.env.VITE_SERVER_IP || '************',
  isProduction: import.meta.env.NODE_ENV === 'production',
  debug: import.meta.env.VITE_DEBUG === 'true'
}

// Admin Configuration (Secured)
export const ADMIN_CONFIG = {
  username: decodeBase64(import.meta.env.VITE_ADMIN_USERNAME_HASH || encodeBase64('admin_khobra_kitchens')),
  email: import.meta.env.VITE_ADMIN_EMAIL || '<EMAIL>',
  password: decodeBase64(import.meta.env.VITE_ADMIN_PASSWORD_HASH || encodeBase64('khobra_admin_2024')),
  sessionTimeout: parseInt(import.meta.env.VITE_SESSION_TIMEOUT || '3600000')
}

// Security Configuration
export const SECURITY_CONFIG = {
  jwtSecret: import.meta.env.VITE_JWT_SECRET || 'khobra_kitchens_jwt_secret_2024_secure',
  encryptionKey: import.meta.env.VITE_ENCRYPTION_KEY || 'khobra_enc_key_2024',
  sessionTimeout: parseInt(import.meta.env.VITE_SESSION_TIMEOUT || '3600000')
}

// SEO Configuration
export const SEO_CONFIG = {
  title: import.meta.env.VITE_SITE_TITLE || 'خبرة المطابخ - تصميم وتنفيذ المطابخ والخزائن',
  description: import.meta.env.VITE_SITE_DESCRIPTION || 'شركة خبرة المطابخ المتخصصة في تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية بأعلى معايير الجودة في المملكة العربية السعودية',
  keywords: import.meta.env.VITE_SITE_KEYWORDS || 'مطابخ,خزائن,تصميم مطابخ,مطابخ عصرية,مطابخ كلاسيكية,خزائن ملابس,تفصيل مطابخ,السعودية',
  author: import.meta.env.VITE_SITE_AUTHOR || 'خبرة المطابخ',
  ogImage: `${APP_CONFIG.url}/images/og-image.jpg`,
  twitterCard: 'summary_large_image'
}

// Company Information
export const COMPANY_CONFIG = {
  name: import.meta.env.VITE_COMPANY_NAME || 'خبرة المطابخ',
  phone: import.meta.env.VITE_COMPANY_PHONE || '+966123456789',
  email: import.meta.env.VITE_COMPANY_EMAIL || '<EMAIL>',
  address: import.meta.env.VITE_COMPANY_ADDRESS || 'الرياض، المملكة العربية السعودية'
}

// Social Media Configuration
export const SOCIAL_CONFIG = {
  twitter: import.meta.env.VITE_TWITTER_URL || 'https://twitter.com/khobrakitchens',
  instagram: import.meta.env.VITE_INSTAGRAM_URL || 'https://instagram.com/khobrakitchens',
  whatsapp: import.meta.env.VITE_WHATSAPP_URL || 'https://wa.me/966123456789',
  snapchat: import.meta.env.VITE_SNAPCHAT_URL || 'https://snapchat.com/add/khobrakitchens',
  tiktok: import.meta.env.VITE_TIKTOK_URL || 'https://tiktok.com/@khobrakitchens'
}

// Analytics Configuration
export const ANALYTICS_CONFIG = {
  googleAnalyticsId: import.meta.env.VITE_GOOGLE_ANALYTICS_ID || '',
  googleTagManagerId: import.meta.env.VITE_GOOGLE_TAG_MANAGER_ID || ''
}

// Validation function
export const validateConfig = () => {
  const errors = []
  
  if (!APP_CONFIG.domain) errors.push('Missing APP_DOMAIN')
  if (!ADMIN_CONFIG.username) errors.push('Missing ADMIN_USERNAME')
  if (!ADMIN_CONFIG.password) errors.push('Missing ADMIN_PASSWORD')
  if (!SEO_CONFIG.title) errors.push('Missing SITE_TITLE')
  
  if (errors.length > 0) {
    console.error('Configuration validation failed:', errors)
    return false
  }
  
  return true
}

// Export utility functions for development
export const DEV_UTILS = {
  encodeBase64,
  decodeBase64,
  generateSecurePassword: () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
    let password = ''
    for (let i = 0; i < 16; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return password
  }
}

// Initialize configuration validation
if (APP_CONFIG.isProduction) {
  validateConfig()
}
