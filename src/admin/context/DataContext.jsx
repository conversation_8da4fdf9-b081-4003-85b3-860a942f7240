import { createContext, useContext, useState, useEffect } from 'react'

const DataContext = createContext()

export const useData = () => {
  const context = useContext(DataContext)
  if (!context) {
    throw new Error('useData must be used within a DataProvider')
  }
  return context
}

export const DataProvider = ({ children }) => {
  // Hero Section Data
  const [heroData, setHeroData] = useState({
    title: 'مطابخ تفوق التوقعات، وخزائن بتصاميم لا تُنسى',
    subtitle: 'نحول مساحات منزلك إلى تحف فنية تجمع بين الجمال والوظيفة بلمسة من الإبداع والحرفية العالية',
    backgroundImage: 'https://readdy.ai/api/search-image?query=luxurious%20modern%20kitchen%20with%20elegant%20design%2C%20marble%20countertops%2C%20wooden%20cabinets%2C%20high-end%20appliances%2C%20soft%20lighting%2C%20spacious%20layout%2C%20minimalist%20style%2C%20professional%20photography%2C%20high%20resolution%2C%20advertisement%20quality&width=1920&height=1080&seq=1&orientation=landscape',
    primaryButtonText: 'شاهد تصاميمنا',
    secondaryButtonText: 'تواصل معنا'
  })

  // Why Choose Us Data
  const [whyChooseUsData, setWhyChooseUsData] = useState({
    title: 'لماذا تختار عجائب الخبراء؟',
    subtitle: 'نحن نقدم أفضل الحلول لتصميم وتنفيذ المطابخ والخزائن بجودة عالية وأسعار منافسة',
    features: [
      {
        id: 1,
        icon: 'ri-award-line',
        title: 'خبرة أكثر من 15 عاماً',
        description: 'فريق من المصممين والحرفيين ذوي الخبرة الطويلة في مجال تصميم وتنفيذ المطابخ والخزائن بأعلى معايير الجودة.',
        gradient: 'from-blue-500 to-cyan-500',
        bgGradient: 'from-blue-50 to-cyan-50',
        number: '15+',
        subtitle: 'سنة خبرة'
      },
      {
        id: 2,
        icon: 'ri-palette-line',
        title: 'تصاميم فريدة ومبتكرة',
        description: 'نقدم تصاميم عصرية تناسب ذوقك واحتياجاتك، مع الاهتمام بأدق التفاصيل لإنشاء مساحات تجمع بين الجمال والوظيفة.',
        gradient: 'from-purple-500 to-pink-500',
        bgGradient: 'from-purple-50 to-pink-50',
        number: '500+',
        subtitle: 'تصميم فريد'
      },
      {
        id: 3,
        icon: 'ri-tools-line',
        title: 'مواد عالية الجودة',
        description: 'نستخدم أفضل الخامات المستوردة والمحلية لضمان متانة ودوام منتجاتنا، مع ضمان يصل إلى 10 سنوات على جميع أعمالنا.',
        gradient: 'from-green-500 to-emerald-500',
        bgGradient: 'from-green-50 to-emerald-50',
        number: '10',
        subtitle: 'سنوات ضمان'
      }
    ]
  })

  // Kitchens Data
  const [kitchensData, setKitchensData] = useState([
    {
      id: 1,
      title: 'مطبخ عصري أنيق',
      description: 'تصميم عصري يجمع بين البساطة والأناقة مع استخدام أفضل الخامات',
      category: 'modern',
      images: [
        'https://readdy.ai/api/search-image?query=elegant%20modern%20kitchen%20with%20island%2C%20white%20cabinets%2C%20marble%20countertops%2C%20pendant%20lights%2C%20wooden%20accents%2C%20spacious%20design%2C%20professional%20photography%2C%20advertisement%20quality&width=600&height=800&seq=2&orientation=portrait',
        'https://readdy.ai/api/search-image?query=modern%20white%20kitchen%20island%20close%20up%2C%20marble%20countertop%20detail%2C%20pendant%20lighting%2C%20professional%20photography&width=800&height=600&seq=21&orientation=landscape'
      ]
    },
    {
      id: 2,
      title: 'مطبخ كلاسيكي فاخر',
      description: 'تصميم كلاسيكي يتميز بالفخامة والتفاصيل الدقيقة',
      category: 'classic',
      images: [
        'https://readdy.ai/api/search-image?query=luxury%20classic%20kitchen%20with%20wooden%20cabinets%2C%20granite%20countertops%2C%20traditional%20design%2C%20elegant%20details%2C%20professional%20photography&width=600&height=800&seq=3&orientation=portrait'
      ]
    }
  ])

  // Cabinets Data
  const [cabinetsData, setCabinetsData] = useState([
    {
      id: 1,
      title: 'خزانة ملابس عصرية',
      description: 'تصميم عصري مع تنظيم داخلي مثالي وإضاءة LED',
      category: 'modern',
      images: [
        'https://readdy.ai/api/search-image?query=modern%20wardrobe%20closet%20design%2C%20sliding%20doors%2C%20organized%20storage%2C%20elegant%20interior%2C%20professional%20photography&width=600&height=800&seq=8&orientation=portrait'
      ]
    }
  ])

  // Footer Data
  const [footerData, setFooterData] = useState({
    socialMedia: [
      { platform: 'twitter', url: 'https://twitter.com', icon: 'ri-twitter-line' },
      { platform: 'snapchat', url: 'https://snapchat.com', icon: 'ri-snapchat-line' },
      { platform: 'instagram', url: 'https://instagram.com', icon: 'ri-instagram-line' },
      { platform: 'whatsapp', url: 'https://whatsapp.com', icon: 'ri-whatsapp-line' },
      { platform: 'tiktok', url: 'https://tiktok.com', icon: 'ri-tiktok-line' }
    ],
    quickLinks: [
      { href: '#home', text: 'الرئيسية' },
      { href: '#why-us', text: 'لماذا نحن' },
      { href: '#kitchens', text: 'المطابخ' },
      { href: '#cabinets', text: 'الخزائن' },
      { href: '#contact', text: 'تواصل معنا' }
    ],
    contactInfo: [
      {
        icon: 'ri-map-pin-line',
        text: 'شارع الملك فهد، الرياض، المملكة العربية السعودية'
      },
      {
        icon: 'ri-phone-line',
        text: '+966 12 345 6789'
      },
      {
        icon: 'ri-mail-line',
        text: '<EMAIL>'
      },
      {
        icon: 'ri-time-line',
        text: 'السبت - الخميس: 9 صباحاً - 9 مساءً'
      }
    ],
    copyright: '© 2024 عجائب الخبراء. جميع الحقوق محفوظة.'
  })

  // Load data from localStorage on mount
  useEffect(() => {
    const savedHeroData = localStorage.getItem('admin_hero_data')
    const savedWhyChooseUsData = localStorage.getItem('admin_why_choose_us_data')
    const savedKitchensData = localStorage.getItem('admin_kitchens_data')
    const savedCabinetsData = localStorage.getItem('admin_cabinets_data')
    const savedFooterData = localStorage.getItem('admin_footer_data')

    if (savedHeroData) setHeroData(JSON.parse(savedHeroData))
    if (savedWhyChooseUsData) setWhyChooseUsData(JSON.parse(savedWhyChooseUsData))
    if (savedKitchensData) setKitchensData(JSON.parse(savedKitchensData))
    if (savedCabinetsData) setCabinetsData(JSON.parse(savedCabinetsData))
    if (savedFooterData) setFooterData(JSON.parse(savedFooterData))
  }, [])

  // Save data to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('admin_hero_data', JSON.stringify(heroData))
  }, [heroData])

  useEffect(() => {
    localStorage.setItem('admin_why_choose_us_data', JSON.stringify(whyChooseUsData))
  }, [whyChooseUsData])

  useEffect(() => {
    localStorage.setItem('admin_kitchens_data', JSON.stringify(kitchensData))
  }, [kitchensData])

  useEffect(() => {
    localStorage.setItem('admin_cabinets_data', JSON.stringify(cabinetsData))
  }, [cabinetsData])

  useEffect(() => {
    localStorage.setItem('admin_footer_data', JSON.stringify(footerData))
  }, [footerData])

  const value = {
    heroData,
    setHeroData,
    whyChooseUsData,
    setWhyChooseUsData,
    kitchensData,
    setKitchensData,
    cabinetsData,
    setCabinetsData,
    footerData,
    setFooterData
  }

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  )
}
