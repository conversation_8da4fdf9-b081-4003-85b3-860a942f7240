<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار لوحة التحكم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .success {
            border-color: #4CAF50;
            background: #e8f5e9;
        }
        .error {
            border-color: #f44336;
            background: #ffebee;
        }
        .link {
            display: inline-block;
            margin: 10px 0;
            padding: 10px 20px;
            background: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .link:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار لوحة التحكم - عجائب الخبراء</h1>
        
        <div class="test-item success">
            <h3>✅ تم إصلاح مشكلة إخفاء المحتوى</h3>
            <p>تم إضافة useEffect لتطبيق كلاس 'visible' على المحتوى لجعله مرئياً</p>
        </div>

        <div class="test-item success">
            <h3>✅ تم فحص جميع المكونات</h3>
            <p>جميع مكونات الأقسام (Dashboard, Hero, Kitchens, Cabinets, etc.) تعمل بشكل صحيح</p>
        </div>

        <div class="test-item success">
            <h3>✅ تم فحص السياق (Context)</h3>
            <p>AuthContext و DataContext يعملان بشكل صحيح</p>
        </div>

        <div class="test-item success">
            <h3>✅ تم فحص CSS</h3>
            <p>جميع أنماط CSS تعمل بشكل صحيح والأنيميشن يعمل</p>
        </div>

        <a href="http://localhost:5173/admin" class="link">افتح لوحة التحكم</a>
        <a href="http://localhost:5173/" class="link">افتح الموقع الرئيسي</a>

        <div class="test-item">
            <h3>بيانات تسجيل الدخول:</h3>
            <p><strong>اسم المستخدم:</strong> admin</p>
            <p><strong>كلمة المرور:</strong> admin123</p>
        </div>

        <div class="test-item">
            <h3>الأقسام المتاحة في لوحة التحكم:</h3>
            <ul>
                <li>الرئيسية - نظرة عامة على النظام</li>
                <li>إدارة الهيرو - تحرير القسم الرئيسي</li>
                <li>لماذا تختارنا - إدارة المميزات</li>
                <li>إدارة المطابخ - معرض المطابخ</li>
                <li>إدارة الخزائن - معرض الخزائن</li>
                <li>إدارة الفوتر - روابط التواصل</li>
                <li>إدارة المستخدمين - بيانات المستخدم</li>
            </ul>
        </div>
    </div>
</body>
</html>
